import logging
import os
import sys
from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.fsm.storage.memory import MemoryStorage
from config import BOT_TOKEN
from handlers import (
    to_vcf, done, start, to_txt, admin, manual, add, delete,
    renamectc, renamefile, merge, split, count, nodup, hide_menu,
    generate, getname, cancel, status,
)
from management import clear_data
from pusat_admin import main_menu as admin_panel
from management import clean_system_message, anti_link
from management.group_detection import GroupDetectionMiddleware
from pusat_admin.bot_middleware import BotStatusMiddleware
from pusat_admin.block_check_middleware import BlockCheckMiddleware
from pusat_admin.sending_mode_middleware import SendingModeMiddleware, SendingModeCallbackMiddleware

# Payment System Integration - NEW SYSTEM
from payment.package_selection_handler import router as package_router
from payment.qr_payment_handler import router as qr_router
from payment.premium_handler import router as premium_router  # Keep for backward compatibility
from payment.payment_checker import PaymentChecker
from payment.premium_expiry_checker import init_expiry_checker
from payment.trial_notification_service import init_trial_notification_service
from payment.premium_middleware import PremiumMiddleware

# Setup logging tanpa tanggal/waktu, pastikan log info tampil di terminal
logging.basicConfig(
    level=logging.INFO,
    format="%(levelname)s %(message)s",
    stream=sys.stdout
)

# Sembunyikan log internal aiogram "Update id=... is handled..." dari terminal
logging.getLogger("aiogram.event").setLevel(logging.WARNING)
logging.getLogger("aiogram.dispatcher").setLevel(logging.WARNING)
logging.getLogger("aiogram.dispatcher.dispatcher").setLevel(logging.WARNING)

# INFO: Logging dari handler utama (folder handlers) tetap aktif
# Background tasks, anti-link, clean system message, dan fitur admin sudah di-disable untuk mengurangi noise

# Pastikan folder data dan management ada
os.makedirs("data", exist_ok=True)
os.makedirs("management", exist_ok=True)
os.makedirs("backups", exist_ok=True)  # Folder untuk backup system

# Global bot instance untuk digunakan di module lain
bot = None
payment_checker = None
expiry_checker = None
trial_notification_service = None



async def main():
    global bot, payment_checker, expiry_checker, trial_notification_service



    logging.info("Bot is starting...")
    bot = Bot(token=BOT_TOKEN)
    dp = Dispatcher(storage=MemoryStorage())

    # Register middleware
    dp.message.middleware(BotStatusMiddleware())
    dp.callback_query.middleware(BotStatusMiddleware())

    # Register group detection middleware (setelah bot status, sebelum block check)
    dp.message.middleware(GroupDetectionMiddleware())

    # Register block check middleware (setelah group detection)
    dp.message.middleware(BlockCheckMiddleware())
    dp.callback_query.middleware(BlockCheckMiddleware())

    # Register sending mode middleware (setelah block check, sebelum premium)
    dp.message.middleware(SendingModeMiddleware())
    dp.callback_query.middleware(SendingModeCallbackMiddleware())

    # Register premium middleware (setelah sending mode check)
    dp.message.middleware(PremiumMiddleware())

    # Register routers
    dp.include_router(cancel.router)
    dp.include_router(start.router)
    dp.include_router(status.router)
    dp.include_router(package_router)
    dp.include_router(qr_router)
    dp.include_router(premium_router)
    dp.include_router(generate.router)
    dp.include_router(getname.router)
    dp.include_router(to_vcf.router)
    dp.include_router(hide_menu.router)
    dp.include_router(to_txt.router)
    dp.include_router(add.router)
    dp.include_router(delete.router)
    dp.include_router(renamectc.router)
    dp.include_router(renamefile.router)
    dp.include_router(merge.router)
    dp.include_router(split.router)
    dp.include_router(count.router)
    dp.include_router(nodup.router)
    dp.include_router(done.router)
    dp.include_router(admin_panel.router)
    dp.include_router(clear_data.router)
    dp.include_router(admin.router)
    dp.include_router(manual.router)
    dp.include_router(clean_system_message.router)
    dp.include_router(anti_link.router)

    # Start background tasks untuk optimisasi
    try:
        from management.background_tasks import start_background_tasks
        await start_background_tasks(bot)
        # logging.info("Background tasks started successfully")  # Disabled untuk mengurangi noise
    except Exception as e:
        logging.warning(f"Failed to start background tasks: {e}")

    # Start payment checker
    try:
        payment_checker = PaymentChecker(bot)
        await payment_checker.start()
        # logging.info("💰 Payment checker started")  # Disabled untuk mengurangi noise
    except Exception as e:
        logging.warning(f"Failed to start payment checker: {e}")

    # Start premium expiry checker
    try:
        expiry_checker = init_expiry_checker(bot)
        await expiry_checker.start()
        # logging.info("⏰ Premium expiry checker started")  # Disabled untuk mengurangi noise
    except Exception as e:
        logging.warning(f"Failed to start expiry checker: {e}")

    # Start trial notification service
    try:
        trial_notification_service = init_trial_notification_service(bot)
        # Send startup trial notifications
        await trial_notification_service.send_startup_trial_notifications()
        # logging.info("🎁 Trial notification service started")  # Disabled untuk mengurangi noise
    except Exception as e:
        logging.warning(f"Failed to start trial notification service: {e}")

    try:
        await dp.start_polling(bot)
    finally:
        # Stop background tasks saat bot berhenti
        try:
            from management.background_tasks import stop_background_tasks
            await stop_background_tasks()
            logging.info("Background tasks stopped")
        except Exception as e:
            logging.warning(f"Error stopping background tasks: {e}")

        # Stop payment checker
        try:
            if payment_checker:
                await payment_checker.stop()
                logging.info("🛑 Payment checker stopped")
        except Exception as e:
            logging.warning(f"Error stopping payment checker: {e}")

        # Stop expiry checker
        try:
            if expiry_checker:
                await expiry_checker.stop()
                logging.info("🛑 Premium expiry checker stopped")
        except Exception as e:
            logging.warning(f"Error stopping expiry checker: {e}")

    logging.info("Bot stopped.")

if __name__ == "__main__":
    import asyncio
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        logging.info("Bot terminated by user.")